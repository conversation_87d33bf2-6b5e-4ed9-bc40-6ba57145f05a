2025-08-02 08:05:57,246 10552 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.request:INFO" 
2025-08-02 08:05:57,246 10552 DEBUG ? odoo.netsvc: logger level set: "odoo.http.rpc.response:INFO" 
2025-08-02 08:05:57,246 10552 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-08-02 08:05:57,246 10552 DEBUG ? odoo.netsvc: logger level set: "odoo:DEBUG" 
2025-08-02 08:05:57,246 10552 DEBUG ? odoo.netsvc: logger level set: "odoo.sql_db:INFO" 
2025-08-02 08:05:57,246 10552 DEBUG ? odoo.netsvc: logger level set: ":INFO" 
2025-08-02 08:05:57,248 10552 WARNING ? py.warnings: E:\midlogic\odoo_erp\odoo\tools\config.py:648: DeprecationWarning: The 'osv_memory_age_limit' option found in the configuration file is a deprecated alias to 'transient_age_limit', please use the latter.
  File "E:\midlogic\odoo_erp\odoo-bin", line 8, in <module>
    odoo.cli.main()
  File "E:\midlogic\odoo_erp\odoo\cli\command.py", line 66, in main
    o.run(args)
  File "E:\midlogic\odoo_erp\odoo\cli\server.py", line 182, in run
    main(args)
  File "E:\midlogic\odoo_erp\odoo\cli\server.py", line 137, in main
    odoo.tools.config.parse_config(args, setup_logging=True)
  File "E:\midlogic\odoo_erp\odoo\tools\config.py", line 437, in parse_config
    self._warn_deprecated_options()
  File "E:\midlogic\odoo_erp\odoo\tools\config.py", line 648, in _warn_deprecated_options
    warnings.warn(
 
2025-08-02 08:05:57,248 10552 INFO ? odoo: Odoo version 18.0 
2025-08-02 08:05:57,248 10552 INFO ? odoo: Using configuration file at E:\midlogic\odoo_erp\odoo.conf 
2025-08-02 08:05:57,249 10552 INFO ? odoo: addons paths: ['E:\\midlogic\\odoo_erp\\odoo\\addons', 'e:\\midlogic\\odoo_erp\\data\\addons\\18.0', 'e:\\midlogic\\odoo_erp\\addons', 'e:\\midlogic\\odoo_erp\\odoo\\addons'] 
2025-08-02 08:05:57,249 10552 INFO ? odoo: database: odoo@localhost:5432 
2025-08-02 08:05:57,370 10552 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-08-02 08:05:57,377 10552 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltoimage to generate images from html. 
2025-08-02 08:05:57,527 10552 INFO ? odoo.service.server: Starting Odoo with ASGI servers (WSGI support has been removed) 
2025-08-02 08:05:57,595 10552 INFO ? odoo.service.asgi_server: ASGI HTTP service running on 0.0.0.0:8069 
2025-08-02 08:05:57,608 10552 WARNING ? py.warnings: E:\midlogic\odoo_erp\venv\Lib\site-packages\websockets\legacy\__init__.py:6: DeprecationWarning: websockets.legacy is deprecated; see https://websockets.readthedocs.io/en/stable/howto/upgrade.html for upgrade instructions
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1012, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1041, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 992, in run
    self._target(*self._args, **self._kwargs)
  File "E:\midlogic\odoo_erp\odoo\service\asgi_server.py", line 83, in run_server
    self.loop.run_until_complete(self._serve_async())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 706, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 677, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2034, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "E:\midlogic\odoo_erp\odoo\service\asgi_server.py", line 57, in _serve_async
    await self.uvicorn_server.serve()
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\server.py", line 71, in serve
    await self._serve(sockets)
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\server.py", line 78, in _serve
    config.load()
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\config.py", line 428, in load
    ws_protocol_class = import_from_string(WS_PROTOCOLS[self.ws])
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\protocols\websockets\auto.py", line 19, in <module>
    from uvicorn.protocols.websockets.websockets_impl import WebSocketProtocol
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\protocols\websockets\websockets_impl.py", line 11, in <module>
    import websockets.legacy.handshake
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\websockets\legacy\__init__.py", line 6, in <module>
    warnings.warn(  # deprecated in 14.0 - 2024-11-09
 
2025-08-02 08:05:57,621 10552 WARNING ? py.warnings: E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\protocols\websockets\websockets_impl.py:17: DeprecationWarning: websockets.server.WebSocketServerProtocol is deprecated
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1012, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1041, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 992, in run
    self._target(*self._args, **self._kwargs)
  File "E:\midlogic\odoo_erp\odoo\service\asgi_server.py", line 83, in run_server
    self.loop.run_until_complete(self._serve_async())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 706, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 677, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2034, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "E:\midlogic\odoo_erp\odoo\service\asgi_server.py", line 57, in _serve_async
    await self.uvicorn_server.serve()
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\server.py", line 71, in serve
    await self._serve(sockets)
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\server.py", line 78, in _serve
    config.load()
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\config.py", line 428, in load
    ws_protocol_class = import_from_string(WS_PROTOCOLS[self.ws])
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\protocols\websockets\auto.py", line 19, in <module>
    from uvicorn.protocols.websockets.websockets_impl import WebSocketProtocol
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\protocols\websockets\websockets_impl.py", line 17, in <module>
    from websockets.server import WebSocketServerProtocol
 
2025-08-02 08:06:10,671 10552 ERROR ? odoo.asgi: Exception during ASGI request handling. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 346, in dispatch
    await odoo_request._post_init()
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 91, in _post_init
    self.session, self.db = await self._get_session_and_dbname()
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 151, in _get_session_and_dbname
    session = http.Session(session_data, session_id or http.generate_session_id(), new=True)
                                                       ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'odoo.http' has no attribute 'generate_session_id'
2025-08-02 08:06:10,683 10552 ERROR ? odoo.asgi: Exception during ASGI request handling. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 346, in dispatch
    await odoo_request._post_init()
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 91, in _post_init
    self.session, self.db = await self._get_session_and_dbname()
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 151, in _get_session_and_dbname
    session = http.Session(session_data, session_id or http.generate_session_id(), new=True)
                                                       ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'odoo.http' has no attribute 'generate_session_id'
2025-08-02 08:06:16,519 10552 WARNING ? py.warnings: E:\midlogic\odoo_erp\venv\Lib\site-packages\websockets\legacy\server.py:1178: DeprecationWarning: remove second argument of ws_handler
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1012, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1041, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 992, in run
    self._target(*self._args, **self._kwargs)
  File "E:\midlogic\odoo_erp\odoo\service\asgi_server.py", line 83, in run_server
    self.loop.run_until_complete(self._serve_async())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 706, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 677, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2034, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\proactor_events.py", line 325, in _loop_reading
    self._data_received(data, length)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\proactor_events.py", line 274, in _data_received
    self._protocol.data_received(data)
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 179, in data_received
    self.handle_websocket_upgrade()
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 194, in handle_websocket_upgrade
    protocol = self.ws_protocol_class(  # type: ignore[call-arg, misc]
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\uvicorn\protocols\websockets\websockets_impl.py", line 105, in __init__
    super().__init__(
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\websockets\legacy\server.py", line 126, in __init__
    self.ws_handler = remove_path_argument(ws_handler)
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\websockets\legacy\server.py", line 1178, in remove_path_argument
    warnings.warn("remove second argument of ws_handler", DeprecationWarning)
 
2025-08-02 08:06:16,523 10552 WARNING ? odoo.addons.web.controllers.websocket: WebSocket authentication failed: No module named 'jwt' 
2025-08-02 08:06:16,523 10552 ERROR ? odoo.asgi: WebSocket error: Authentication failed: No module named 'jwt' 
2025-08-02 08:06:16,898 10552 DEBUG ? odoo.http: HTTP sessions stored in: e:\midlogic\odoo_erp\data\sessions 
2025-08-02 08:06:16,899 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:16,947 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:16,983 10552 DEBUG ? odoo.modules.module: module __init__.py: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-08-02 08:06:16,983 10552 DEBUG ? odoo.modules.module: module __pycache__: no manifest file found ('__manifest__.py', '__openerp__.py') 
2025-08-02 08:06:17,736 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=2/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=odoo_test host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:17,737 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=1/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:17,863 10552 ERROR ? odoo.asgi: Error serving no-database request 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 396, in _serve_nodb
    response = odoo_request.dispatcher.dispatch(rule.endpoint, args)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 3182, in dispatch
    return endpoint(**self.request.params)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1194, in route_wrapper
    return Response.load(result)
           ~~~~~~~~~~~~~^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\tools\facade.py", line 71, in wrap_func
    return func(*args, **kwargs)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 2020, in load
    return cls(result)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1981, in __init__
    super().__init__(content=content, status_code=status_code, headers=headers,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    template=template, qcontext=qcontext, uid=uid, **kw)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1924, in __init__
    super().__init__(content=content, status_code=status_code,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    headers=headers, media_type=media_type)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\starlette\responses.py", line 47, in __init__
    self.body = self.render(content)
                ~~~~~~~~~~~^^^^^^^^^
TypeError: _Response.render() takes 1 positional argument but 2 were given
2025-08-02 08:06:20,394 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:20,436 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:21,124 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=2/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=odoo_test host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:21,125 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=1/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:21,167 10552 ERROR ? odoo.asgi: Error serving no-database request 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 396, in _serve_nodb
    response = odoo_request.dispatcher.dispatch(rule.endpoint, args)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 3182, in dispatch
    return endpoint(**self.request.params)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1194, in route_wrapper
    return Response.load(result)
           ~~~~~~~~~~~~~^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\tools\facade.py", line 71, in wrap_func
    return func(*args, **kwargs)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 2020, in load
    return cls(result)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1981, in __init__
    super().__init__(content=content, status_code=status_code, headers=headers,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    template=template, qcontext=qcontext, uid=uid, **kw)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1924, in __init__
    super().__init__(content=content, status_code=status_code,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    headers=headers, media_type=media_type)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\starlette\responses.py", line 47, in __init__
    self.body = self.render(content)
                ~~~~~~~~~~~^^^^^^^^^
TypeError: _Response.render() takes 1 positional argument but 2 were given
2025-08-02 08:06:25,381 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:25,423 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:26,125 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=2/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=odoo_test host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:26,125 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=1/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:26,170 10552 ERROR ? odoo.asgi: Error serving no-database request 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 396, in _serve_nodb
    response = odoo_request.dispatcher.dispatch(rule.endpoint, args)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 3182, in dispatch
    return endpoint(**self.request.params)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1194, in route_wrapper
    return Response.load(result)
           ~~~~~~~~~~~~~^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\tools\facade.py", line 71, in wrap_func
    return func(*args, **kwargs)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 2020, in load
    return cls(result)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1981, in __init__
    super().__init__(content=content, status_code=status_code, headers=headers,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    template=template, qcontext=qcontext, uid=uid, **kw)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1924, in __init__
    super().__init__(content=content, status_code=status_code,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    headers=headers, media_type=media_type)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\starlette\responses.py", line 47, in __init__
    self.body = self.render(content)
                ~~~~~~~~~~~^^^^^^^^^
TypeError: _Response.render() takes 1 positional argument but 2 were given
2025-08-02 08:06:26,172 10552 ERROR ? odoo.asgi: Exception during ASGI request handling. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 346, in dispatch
    await odoo_request._post_init()
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 91, in _post_init
    self.session, self.db = await self._get_session_and_dbname()
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 151, in _get_session_and_dbname
    session = http.Session(session_data, session_id or http.generate_session_id(), new=True)
                                                       ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'odoo.http' has no attribute 'generate_session_id'
2025-08-02 08:06:26,183 10552 ERROR ? odoo.asgi: Exception during ASGI request handling. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 346, in dispatch
    await odoo_request._post_init()
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 91, in _post_init
    self.session, self.db = await self._get_session_and_dbname()
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 151, in _get_session_and_dbname
    session = http.Session(session_data, session_id or http.generate_session_id(), new=True)
                                                       ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'odoo.http' has no attribute 'generate_session_id'
2025-08-02 08:06:31,384 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:31,426 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:32,103 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=2/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=odoo_test host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:32,103 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=1/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:32,146 10552 ERROR ? odoo.asgi: Error serving no-database request 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 396, in _serve_nodb
    response = odoo_request.dispatcher.dispatch(rule.endpoint, args)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 3182, in dispatch
    return endpoint(**self.request.params)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1194, in route_wrapper
    return Response.load(result)
           ~~~~~~~~~~~~~^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\tools\facade.py", line 71, in wrap_func
    return func(*args, **kwargs)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 2020, in load
    return cls(result)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1981, in __init__
    super().__init__(content=content, status_code=status_code, headers=headers,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    template=template, qcontext=qcontext, uid=uid, **kw)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1924, in __init__
    super().__init__(content=content, status_code=status_code,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    headers=headers, media_type=media_type)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\starlette\responses.py", line 47, in __init__
    self.body = self.render(content)
                ~~~~~~~~~~~^^^^^^^^^
TypeError: _Response.render() takes 1 positional argument but 2 were given
2025-08-02 08:06:40,690 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:40,748 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:41,453 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=2/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=odoo_test host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:41,454 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=1/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:41,496 10552 ERROR ? odoo.asgi: Error serving no-database request 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 396, in _serve_nodb
    response = odoo_request.dispatcher.dispatch(rule.endpoint, args)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 3182, in dispatch
    return endpoint(**self.request.params)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1194, in route_wrapper
    return Response.load(result)
           ~~~~~~~~~~~~~^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\tools\facade.py", line 71, in wrap_func
    return func(*args, **kwargs)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 2020, in load
    return cls(result)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1981, in __init__
    super().__init__(content=content, status_code=status_code, headers=headers,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    template=template, qcontext=qcontext, uid=uid, **kw)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1924, in __init__
    super().__init__(content=content, status_code=status_code,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    headers=headers, media_type=media_type)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\starlette\responses.py", line 47, in __init__
    self.body = self.render(content)
                ~~~~~~~~~~~^^^^^^^^^
TypeError: _Response.render() takes 1 positional argument but 2 were given
2025-08-02 08:06:54,379 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:54,432 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:06:55,126 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=2/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=odoo_test host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:55,126 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=1/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:06:55,174 10552 ERROR ? odoo.asgi: Error serving no-database request 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 396, in _serve_nodb
    response = odoo_request.dispatcher.dispatch(rule.endpoint, args)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 3182, in dispatch
    return endpoint(**self.request.params)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1194, in route_wrapper
    return Response.load(result)
           ~~~~~~~~~~~~~^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\tools\facade.py", line 71, in wrap_func
    return func(*args, **kwargs)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 2020, in load
    return cls(result)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1981, in __init__
    super().__init__(content=content, status_code=status_code, headers=headers,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    template=template, qcontext=qcontext, uid=uid, **kw)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1924, in __init__
    super().__init__(content=content, status_code=status_code,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    headers=headers, media_type=media_type)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\starlette\responses.py", line 47, in __init__
    self.body = self.render(content)
                ~~~~~~~~~~~^^^^^^^^^
TypeError: _Response.render() takes 1 positional argument but 2 were given
2025-08-02 08:07:14,682 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:07:14,724 10552 DEBUG ? odoo.tools._vendor.sessions: Could not load session from disk. Use empty session. 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\tools\_vendor\sessions.py", line 216, in get
    f = open(self.get_session_filename(sid), "r", encoding="utf-8")
FileNotFoundError: [Errno 2] No such file or directory: 'e:\\midlogic\\odoo_erp\\data\\sessions\\f0\\f0VqopzDG5fj7T4uIShzGBbay6ohb-qZ9yT5z2TbbYhu6dsQdVilhHPuHLfVdjzoipHYRNAeBRxaBvcMwjCq'
2025-08-02 08:07:15,425 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=2/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=odoo_test host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:07:15,426 10552 INFO ? odoo.sql_db: ConnectionPool(read/write;used=0/count=1/max=64): Closed 1 connections to 'user=odoo password=xxx dbname=test_db host=localhost port=5432 application_name=odoo-10552 sslmode=prefer' 
2025-08-02 08:07:15,467 10552 ERROR ? odoo.asgi: Error serving no-database request 
Traceback (most recent call last):
  File "E:\midlogic\odoo_erp\odoo\asgi.py", line 396, in _serve_nodb
    response = odoo_request.dispatcher.dispatch(rule.endpoint, args)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 3182, in dispatch
    return endpoint(**self.request.params)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1194, in route_wrapper
    return Response.load(result)
           ~~~~~~~~~~~~~^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\tools\facade.py", line 71, in wrap_func
    return func(*args, **kwargs)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 2020, in load
    return cls(result)
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1981, in __init__
    super().__init__(content=content, status_code=status_code, headers=headers,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    template=template, qcontext=qcontext, uid=uid, **kw)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\odoo\http.py", line 1924, in __init__
    super().__init__(content=content, status_code=status_code,
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                    headers=headers, media_type=media_type)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\midlogic\odoo_erp\venv\Lib\site-packages\starlette\responses.py", line 47, in __init__
    self.body = self.render(content)
                ~~~~~~~~~~~^^^^^^^^^
TypeError: _Response.render() takes 1 positional argument but 2 were given
