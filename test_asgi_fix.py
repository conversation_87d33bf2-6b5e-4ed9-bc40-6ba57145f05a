#!/usr/bin/env python3
"""
Test script to verify ASGI fixes
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_asgi_imports():
    """Test that ASGI modules can be imported without errors"""
    try:
        import odoo.asgi
        print("✅ odoo.asgi imported successfully")
        
        # Test ASGIRequest class
        from odoo.asgi import ASGIRequest
        print("✅ ASGIRequest class imported successfully")
        
        # Test that ASGIRequest has the required methods
        if hasattr(ASGIRequest, 'get_http_params'):
            print("✅ ASGIRequest.get_http_params method exists")
        else:
            print("❌ ASGIRequest.get_http_params method missing")
            
        if hasattr(ASGIRequest, 'get_json_data'):
            print("✅ ASGIRequest.get_json_data method exists")
        else:
            print("❌ ASGIRequest.get_json_data method missing")
            
        return True
    except Exception as e:
        print(f"❌ Error importing ASGI modules: {e}")
        return False

def test_http_imports():
    """Test that HTTP modules can be imported without errors"""
    try:
        import odoo.http
        print("✅ odoo.http imported successfully")
        
        # Test request stack
        if hasattr(odoo.http, '_request_stack'):
            print("✅ _request_stack exists")
        else:
            print("❌ _request_stack missing")
            
        if hasattr(odoo.http, 'request'):
            print("✅ request object exists")
        else:
            print("❌ request object missing")
            
        return True
    except Exception as e:
        print(f"❌ Error importing HTTP modules: {e}")
        return False

def main():
    """Main test function"""
    print("Testing ASGI fixes...")
    print("=" * 50)
    
    asgi_ok = test_asgi_imports()
    print()
    http_ok = test_http_imports()
    
    print()
    print("=" * 50)
    if asgi_ok and http_ok:
        print("✅ All tests passed! ASGI fixes appear to be working.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
